# Stimulus Rails 反馈状态管理使用指南

## 概述

我们已经成功将驱动反馈的状态管理功能从传统的内联JavaScript迁移到了Stimulus Rails框架。这提供了更好的代码组织、维护性和测试能力。

## 文件结构

```
app/javascript/controllers/
└── feedback_status_controller.js    # Stimulus 控制器

app/views/operation/feedbacks/
├── index.html.erb                   # 列表页面（使用 Stimulus）
└── show.html.erb                    # 详情页面（使用 Stimulus）

app/controllers/operation/
└── feedbacks_controller.rb          # 后端控制器

app/policies/
└── feedback_policy.rb               # 权限策略
```

## Stimulus 控制器功能

### 1. 数据属性配置

**控制器标识符**: `feedback-status`

**值 (Values)**:
- `feedbackId`: 反馈ID（数字类型）
- `updateUrl`: 更新状态的URL（字符串类型）

**目标 (Targets)**:
- `statusSelect`: 状态选择下拉框
- `updateButton`: 更新按钮

**动作 (Actions)**:
- `updateStatus`: 快速状态更新（列表页面）
- `updateDetailStatus`: 详情页面状态更新

### 2. 列表页面使用方式

```html
<!-- 快速状态修改下拉菜单 -->
<div class="btn-group" data-controller="feedback-status">
  <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
          data-bs-toggle="dropdown" aria-expanded="false">
    状态
  </button>
  <ul class="dropdown-menu">
    <li><a class="dropdown-item" href="#" 
           data-feedback-id="<%= feedback.id %>" 
           data-status="pending"
           data-action="click->feedback-status#updateStatus">待处理</a></li>
    <li><a class="dropdown-item" href="#" 
           data-feedback-id="<%= feedback.id %>" 
           data-status="processing"
           data-action="click->feedback-status#updateStatus">处理中</a></li>
    <li><a class="dropdown-item" href="#" 
           data-feedback-id="<%= feedback.id %>" 
           data-status="completed"
           data-action="click->feedback-status#updateStatus">已处理</a></li>
  </ul>
</div>
```

### 3. 详情页面使用方式

```html
<div data-controller="feedback-status" 
     data-feedback-status-feedback-id-value="<%= @feedback.id %>"
     data-feedback-status-update-url-value="<%= update_status_operation_feedback_path(@feedback) %>">
  <label for="status-select" class="form-label"><strong>修改状态:</strong></label>
  <select id="status-select" 
          class="form-select d-inline-block" 
          style="width: auto;"
          data-feedback-status-target="statusSelect">
    <option value="pending" <%= 'selected' if @feedback.status == 'pending' %>>待处理</option>
    <option value="processing" <%= 'selected' if @feedback.status == 'processing' %>>处理中</option>
    <option value="completed" <%= 'selected' if @feedback.status == 'completed' %>>已处理</option>
  </select>
  <button class="btn btn-primary btn-sm ms-2"
          data-feedback-status-target="updateButton"
          data-action="click->feedback-status#updateDetailStatus">更新状态</button>
</div>
```

## 技术特性

### 1. AJAX 请求处理
- 使用 `@rails/request.js` 进行 AJAX 请求
- 自动处理 CSRF token
- 支持 JSON 响应格式

### 2. 错误处理
- 网络错误处理
- 服务器错误响应处理
- 用户友好的错误提示

### 3. 用户体验优化
- 成功操作后显示提示信息
- 延迟刷新页面以显示提示
- 防止重复提交

### 4. 权限控制
- 后端权限检查：`authorize @feedback, :update_status?`
- 策略方法：`FeedbackPolicy#update_status?`

## 开发和调试

### 1. 控制器连接确认
控制器连接时会在控制台输出：
```
Feedback status controller connected
```

### 2. 错误日志
所有错误都会记录到浏览器控制台，便于调试。

### 3. 扩展功能
如需添加新功能，可以在 `feedback_status_controller.js` 中添加新的方法，并在视图中通过 `data-action` 绑定。

## 优势对比

### 传统内联 JavaScript vs Stimulus Rails

| 特性 | 内联 JavaScript | Stimulus Rails |
|------|----------------|----------------|
| 代码组织 | 分散在视图中 | 集中在控制器文件 |
| 复用性 | 难以复用 | 高度可复用 |
| 测试能力 | 难以测试 | 易于单元测试 |
| 维护性 | 维护困难 | 易于维护 |
| 生命周期管理 | 手动管理 | 自动管理 |
| 与 Rails 集成 | 需要手动处理 | 深度集成 |

## 最佳实践

1. **命名约定**: 使用 kebab-case 命名控制器和动作
2. **数据属性**: 使用语义化的数据属性名称
3. **错误处理**: 始终提供用户友好的错误提示
4. **权限检查**: 在后端进行权限验证
5. **渐进增强**: 确保在 JavaScript 禁用时功能仍可用

这个实现为驱动反馈状态管理提供了现代化、可维护的解决方案，符合 Rails 7 和 Stimulus 的最佳实践。
