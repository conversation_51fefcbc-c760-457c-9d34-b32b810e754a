module Operation
  class FeedbacksController < OperationController
    before_action do
      authorize Feedback
    end

    before_action :set_feedback, only: [:show, :destroy, :update_status]

    def index
      @q = Feedback.ransack(params[:q])
      @q.sorts = 'created_at desc' if @q.sorts.empty?
      feedbacks = @q.result(distinct: true)
      @pagy, @feedbacks = pagy(feedbacks, items: 10)
    end

    def show
    end

    def destroy
      if @feedback.destroy
        notice = '删除成功!'
      else
        notice = '删除失败!'
      end
      redirect_to operation_feedbacks_path, notice: notice
    end

    def update_status
      authorize @feedback, :update_status?

      if @feedback.update(status: params[:status])
        if request.xhr?
          render json: {
            success: true,
            message: '状态更新成功',
            status: @feedback.status,
            status_i18n: @feedback.status_i18n
          }
        else
          redirect_to operation_feedback_path(@feedback), notice: '状态更新成功'
        end
      else
        if request.xhr?
          render json: {
            success: false,
            message: '状态更新失败',
            errors: @feedback.errors.full_messages
          }
        else
          redirect_to operation_feedback_path(@feedback), alert: '状态更新失败'
        end
      end
    end

    private

    def set_feedback
      @feedback = Feedback.find(params[:id])
    end

    def feedback_params
      params.require(:feedback).permit(:contact, :description, :pid, :sn_code, :version, :status, images: [])
    end
  end
end
