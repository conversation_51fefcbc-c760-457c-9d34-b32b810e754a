import { Controller } from "@hotwired/stimulus"
import { FetchRequest } from "@rails/request.js"

// Connects to data-controller="feedback-status"
export default class extends Controller {
  static targets = ["statusSelect", "updateButton"]
  static values = {
    feedbackId: Number,
    updateUrl: String
  }

  connect() {
    console.log("Feedback status controller connected")
  }

  // 处理快速状态更新（列表页面）
  async updateStatus(event) {
    event.preventDefault()

    const link = event.currentTarget
    const feedbackId = link.dataset.feedbackId
    const status = link.dataset.status
    const statusText = link.textContent.trim()

    try {
      const request = new FetchRequest("PATCH", `/operation/feedbacks/${feedbackId}/update_status`, {
        headers: {
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest"
        },
        body: JSON.stringify({ status: status })
      })

      const response = await request.perform()

      if (response.ok) {
        const data = await response.json
        if (data.success) {
          this.showSuccess('状态更新成功')
          // 刷新页面以显示更新后的状态
          setTimeout(() => window.location.reload(), 500)
        } else {
          this.showError(data.message || '状态更新失败')
        }
      } else {
        this.showError('网络请求失败')
      }
    } catch (error) {
      console.error('Error:', error)
      this.showError('状态更新失败，请稍后重试')
    }
  }

  // 处理详情页面的状态更新
  async updateDetailStatus() {
    if (!this.hasStatusSelectTarget) {
      console.error('Status select target not found')
      return
    }

    const newStatus = this.statusSelectTarget.value

    try {
      const request = new FetchRequest("PATCH", this.updateUrlValue, {
        headers: {
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest"
        },
        body: JSON.stringify({ status: newStatus })
      })

      const response = await request.perform()

      if (response.ok) {
        const data = await response.json
        if (data.success) {
          this.showSuccess('状态更新成功')
          // 刷新页面以显示更新后的状态
          setTimeout(() => window.location.reload(), 500)
        } else {
          this.showError(data.message || '状态更新失败')
        }
      } else {
        this.showError('网络请求失败')
      }
    } catch (error) {
      console.error('Error:', error)
      this.showError('状态更新失败，请稍后重试')
    }
  }

  // 显示错误信息
  showError(message) {
    // 可以使用 toast 或者 alert 显示错误信息
    if (typeof window.showToast === 'function') {
      window.showToast(message, 'error')
    } else {
      alert(message)
    }
  }

  // 显示成功信息
  showSuccess(message) {
    if (typeof window.showToast === 'function') {
      window.showToast(message, 'success')
    } else {
      console.log(message)
    }
  }
}
