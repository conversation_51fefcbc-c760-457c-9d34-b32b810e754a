class Uploads < Base
  namespace :uploads do
    desc "上传图片"
    params do
      requires :image, type: File, desc: "图片文件"
      optional :token, type: String, desc: "用户Token (可选)"
    end
    post "image" do
      begin
        # 验证文件类型
        unless params[:image].present?
          return {
            success: false,
            message: "请选择要上传的图片",
            code: 400
          }
        end

        # 检查文件扩展名
        filename = params[:image][:filename]
        ext = File.extname(filename).downcase
        allowed_extensions = %w(.jpg .jpeg .png .gif .webp)

        unless allowed_extensions.include?(ext)
          return {
            success: false,
            message: "不支持的图片格式，请上传 JPG、PNG、GIF 或 WEBP 格式的图片",
            code: 400
          }
        end

        # 检查文件大小 (10MB)
        # if params[:image][:tempfile].size > 10.megabytes
        #   return {
        #     success: false,
        #     message: "图片文件大小不能超过 10MB",
        #     code: 400
        #   }
        # end

        # 创建上传器实例
        uploader = APIImageUploader.new
        uploader.store!(params[:image][:tempfile])

        # 构建响应数据
        response_data = {
          success: true,
          message: "图片上传成功",
          code: 200,
          data: {
            url: uploader.url,
            # thumb_url: uploader.thumb.url,
            # medium_url: uploader.medium.url,
            filename: filename,
            size: params[:image][:tempfile].size,
            uploaded_at: Time.current.iso8601
          }
        }

        # 如果提供了用户token，可以记录上传日志
        if params[:token].present?
          user = User.user_status.where(authentication_token: params[:token]).first
          if user
            response_data[:data][:user_id] = user.id
            # 这里可以添加上传日志记录逻辑
            Rails.logger.info "User #{user.id} uploaded image: #{uploader.url}"
          end
        end

        response_data

      rescue CarrierWave::IntegrityError => e
        {
          success: false,
          message: "图片文件格式不正确",
          code: 400
        }
      rescue CarrierWave::ProcessingError => e
        {
          success: false,
          message: "图片处理失败，请检查文件是否损坏",
          code: 400
        }
      rescue => e
        Rails.logger.error "Image upload error: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")

        {
          success: false,
          message: "图片上传失败，请稍后重试",
          code: 500
        }
      end
    end
  end
end
