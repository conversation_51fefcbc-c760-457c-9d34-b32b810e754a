# frozen_string_literal: true

class Feedbacks < Base
  resources :feedbacks do
    desc '创建反馈' do
      detail '用户提交产品反馈信息'
      success code: 201, message: '创建成功'
    end
    params do
      optional :contact, type: String, desc: '联系方式'
      requires :description, type: String, desc: '问题描述'
      optional :images, type: Array, desc: '反馈图片'
      optional :pid, type: Integer, desc: '产品ID'
      optional :sn_code, type: String, desc: '产品序列号'
      optional :version, type: String, desc: '产品版本'
    end
    post '/' do
      feedback = Feedback.new(
        contact: params[:contact],
        description: params[:description],
        images: params[:images],
        pid: params[:pid],
        sn_code: params[:sn_code],
        version: params[:version],
        ip: request.ip
      )

      if feedback.save
        status 201
        {
          status: 201,
          message: '反馈提交成功',
          data: {
            id: feedback.id,
            created_at: feedback.created_at
          }
        }
      else
        bad_request!('反馈提交失败')
      end
    end

  #   desc '获取反馈列表' do
  #     detail '获取反馈列表（需要管理员权限）'
  #     success code: 200, message: '获取成功'
  #   end
  #   params do
  #     use :paginate
  #     optional :pid, type: Integer, desc: '产品ID筛选'
  #     optional :sn_code, type: String, desc: '序列号筛选'
  #   end
  #   get '/' do
  #     # 这个接口可能需要管理员权限，暂时开放
  #     feedbacks = Feedback.all
  #     feedbacks = feedbacks.where(pid: params[:pid]) if params[:pid].present?
  #     feedbacks = feedbacks.where(sn_code: params[:sn_code]) if params[:sn_code].present?

  #     feedbacks = feedbacks.order(created_at: :desc)

  #     # 简单分页
  #     page = params[:page] || 1
  #     per = params[:per] || 10
  #     offset = (page - 1) * per

  #     total_count = feedbacks.count
  #     feedbacks = feedbacks.limit(per).offset(offset)

  #     {
  #       status: 200,
  #       data: feedbacks.map do |feedback|
  #         {
  #           id: feedback.id,
  #           contact: feedback.contact,
  #           description: feedback.description,
  #           images: feedback.images,
  #           pid: feedback.pid,
  #           sn_code: feedback.sn_code,
  #           version: feedback.version,
  #           ip: feedback.ip,
  #           created_at: feedback.created_at,
  #           updated_at: feedback.updated_at
  #         }
  #       end,
  #       pagination: {
  #         current_page: page,
  #         per_page: per,
  #         total_count: total_count,
  #         total_pages: (total_count.to_f / per).ceil
  #       }
  #     }
  #   end

  #   desc '获取反馈详情' do
  #     detail '根据ID获取反馈详情'
  #     success code: 200, message: '获取成功'
  #   end
  #   params do
  #     requires :id, type: Integer, desc: '反馈ID'
  #   end
  #   get ':id' do
  #     feedback = Feedback.find_by(id: params[:id])
  #     not_found!('反馈不存在') if feedback.blank?

  #     {
  #       status: 200,
  #       data: {
  #         id: feedback.id,
  #         contact: feedback.contact,
  #         description: feedback.description,
  #         images: feedback.images,
  #         pid: feedback.pid,
  #         sn_code: feedback.sn_code,
  #         version: feedback.version,
  #         ip: feedback.ip,
  #         created_at: feedback.created_at,
  #         updated_at: feedback.updated_at
  #       }
  #     }
  #   end
  end
end
