<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">管理</a></li>
            <li class="breadcrumb-item"><%= link_to "驱动反馈", operation_feedbacks_path %></li>
            <li class="breadcrumb-item active">反馈详情</li>
          </ol>
        </div>
        <h4 class="page-title">反馈详情</h4>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h5>基本信息</h5>
              <table class="table table-borderless">
                <tr>
                  <td width="120"><strong>反馈ID:</strong></td>
                  <td><%= @feedback.id %></td>
                </tr>
                <tr>
                  <td><strong>联系方式:</strong></td>
                  <td><%= @feedback.contact.present? ? @feedback.contact : "无" %></td>
                </tr>
                <tr>
                  <td><strong>设备型号:</strong></td>
                  <td><%= @feedback.pid.present? ? Device.find_by(pid: @feedback.pid)&.name : "无" %></td>
                </tr>
                <tr>
                  <td><strong>序列号:</strong></td>
                  <td><%= @feedback.sn_code.present? ? @feedback.sn_code : "无" %></td>
                </tr>
                <tr>
                  <td><strong>固件版本:</strong></td>
                  <td><%= @feedback.version.present? ? @feedback.version : "无" %></td>
                </tr>
                <tr>
                  <td><strong>IP地址:</strong></td>
                  <td><%= @feedback.ip.present? ? @feedback.ip : "无" %></td>
                </tr>
                <tr>
                  <td><strong>提交时间:</strong></td>
                  <td><%= @feedback.created_at.strftime("%Y-%m-%d %H:%M:%S") %></td>
                </tr>
                <tr>
                  <td><strong>更新时间:</strong></td>
                  <td><%= @feedback.updated_at.strftime("%Y-%m-%d %H:%M:%S") %></td>
                </tr>
                <tr>
                  <td><strong>处理状态:</strong></td>
                  <td>
                    <% case @feedback.status %>
                    <% when 'pending' %>
                      <span class="badge bg-warning">待处理</span>
                    <% when 'processing' %>
                      <span class="badge bg-info">处理中</span>
                    <% when 'completed' %>
                      <span class="badge bg-success">已处理</span>
                    <% end %>
                  </td>
                </tr>
              </table>
            </div>

            <div class="col-md-6">
              <h5>问题描述</h5>
              <div class="border p-3 rounded">
                <%= simple_format(@feedback.description) %>
              </div>

              <% if @feedback.images.present? %>
                <h5 class="mt-4">反馈图片</h5>
                <div class="row">
                  <% @feedback.images.each_with_index do |image, index| %>
                    <div class="col-md-4 mb-3">
                      <div class="card">
                        <img src="<%= image %>" class="card-img-top" alt="反馈图片 <%= index + 1 %>" style="height: 200px; object-fit: cover;">
                        <div class="card-body p-2">
                          <small class="text-muted">图片 <%= index + 1 %></small>
                          <br>
                          <a href="<%= image %>" target="_blank" class="btn btn-sm btn-outline-primary">查看原图</a>
                        </div>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>

          <div class="row mt-4">
            <div class="col-12">
              <div class="d-flex justify-content-between align-items-center">
                <div data-controller="feedback-status"
                     data-feedback-status-feedback-id-value="<%= @feedback.id %>"
                     data-feedback-status-update-url-value="<%= update_status_operation_feedback_path(@feedback) %>">
                  <label for="status-select" class="form-label"><strong>修改状态:</strong></label>
                  <select id="status-select"
                          class="form-select d-inline-block"
                          style="width: auto;"
                          data-feedback-status-target="statusSelect">
                    <option value="pending" <%= 'selected' if @feedback.status == 'pending' %>>待处理</option>
                    <option value="processing" <%= 'selected' if @feedback.status == 'processing' %>>处理中</option>
                    <option value="completed" <%= 'selected' if @feedback.status == 'completed' %>>已处理</option>
                  </select>
                  <button class="btn btn-primary btn-sm ms-2"
                          data-feedback-status-target="updateButton"
                          data-action="click->feedback-status#updateDetailStatus">更新状态</button>
                </div>
                <div>
                  <%= link_to "返回列表", operation_feedbacks_path, class: "btn btn-secondary" %>
                  <%#= link_to "删除", operation_feedback_path(@feedback),
                      method: :delete,
                      class: "btn btn-danger",
                      data: { confirm: "确定要删除这条反馈吗？" } %>
                </div>
              </div>
            </div>
          </div>
        </div> <!-- end card-body-->
      </div> <!-- end card-->
    </div> <!-- end col -->
  </div>
</div>
