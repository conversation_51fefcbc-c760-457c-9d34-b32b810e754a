<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">管理</a></li>
            <li class="breadcrumb-item active">驱动反馈</li>
          </ol>
        </div>
        <h4 class="page-title">驱动反馈</h4>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <!-- 搜索表单 -->
          <%= search_form_for @q, url: operation_feedbacks_path, method: :get, local: true do |f| %>
            <div class="row mb-3">
              <div class="col-md-2">
                <%= f.text_field :contact_cont, placeholder: "联系方式", class: "form-control" %>
              </div>
              <div class="col-md-2">
                <%= f.text_field :sn_code_cont, placeholder: "序列号", class: "form-control" %>
              </div>
              <div class="col-md-2">
                <%= f.number_field :pid_eq, placeholder: "设备型号", class: "form-control" %>
              </div>
              <div class="col-md-2">
                <%= f.text_field :version_cont, placeholder: "固件版本", class: "form-control" %>
              </div>
              <div class="col-md-2">
                <%= f.select :status_eq,
                    options_for_select([['全部状态', ''], ['待处理', 'pending'], ['处理中', 'processing'], ['已处理', 'completed']], params.dig(:q, :status_eq)),
                    {},
                    { class: "form-control" } %>
              </div>
              <div class="col-md-2">
                <%= f.submit "搜索", class: "btn btn-primary" %>
                <%= link_to "重置", operation_feedbacks_path, class: "btn btn-secondary" %>
              </div>
            </div>
          <% end %>

          <div class="table-responsive">
            <table class="table table-centered mb-0">
              <thead class="table-light">
                <tr>
                  <th>ID</th>
                  <th>联系方式</th>
                  <th>问题描述</th>
                  <th>设备型号</th>
                  <th>状态</th>
                  <th>提交时间</th>
                  <th style="width: 180px;">操作</th>
                </tr>
              </thead>
              <tbody>
                <% if @feedbacks.blank? %>
                  <tr>
                    <td colspan="10" style="text-align: center;">
                      暂无数据
                    </td>
                  </tr>
                <% end %>
                <% @feedbacks.each do |feedback| %>
                  <tr>
                    <td><%= feedback.id %></td>
                    <td><%= feedback.contact.present? ? feedback.contact : "无" %></td>
                    <td>
                      <span title="<%= feedback.description %>">
                        <%= truncate(feedback.description, length: 50) %>
                      </span>
                    </td>
                    <td><%= feedback.pid.present? ? Device.find_by(pid: feedback.pid)&.name : "无" %></td>
                    <td>
                      <% case feedback.status %>
                      <% when 'pending' %>
                        <span class="badge bg-warning">待处理</span>
                      <% when 'processing' %>
                        <span class="badge bg-info">处理中</span>
                      <% when 'completed' %>
                        <span class="badge bg-success">已处理</span>
                      <% end %>
                    </td>
                    <td><%= feedback.created_at.strftime("%Y-%m-%d %H:%M:%S") %></td>
                    <td>
                      <%= link_to operation_feedback_path(feedback), class: "btn btn-sm btn-info", title: "查看" do %>
                        <i class="mdi mdi-eye"></i>
                      <% end %>

                      <!-- 快速状态修改下拉菜单 -->
                      <div class="btn-group" data-controller="feedback-status">
                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                          状态
                        </button>
                        <ul class="dropdown-menu">
                          <li><a class="dropdown-item" href="#"
                                 data-feedback-id="<%= feedback.id %>"
                                 data-status="pending"
                                 data-action="click->feedback-status#updateStatus">待处理</a></li>
                          <li><a class="dropdown-item" href="#"
                                 data-feedback-id="<%= feedback.id %>"
                                 data-status="processing"
                                 data-action="click->feedback-status#updateStatus">处理中</a></li>
                          <li><a class="dropdown-item" href="#"
                                 data-feedback-id="<%= feedback.id %>"
                                 data-status="completed"
                                 data-action="click->feedback-status#updateStatus">已处理</a></li>
                        </ul>
                      </div>

                      <%#= link_to operation_feedback_path(feedback),
                          method: :delete,
                          class: "btn btn-sm btn-danger",
                          data: { confirm: "确定要删除这条反馈吗？" },
                          title: "删除" do %>
                        <%# <i class="mdi mdi-delete"></i> %>
                      <%# end %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>

          <br>
          <div style="display: flex;">
            <div>
              <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
            </div>
            <div style="flex: 1;text-align: right;">
              <% if @pagy.count <= 10 %>
                共 <b><%= @pagy.count %></b> 项
              <% else %>
                <%== pagy_info(@pagy) %>
              <% end %>
            </div>
          </div>
        </div> <!-- end card-body-->
      </div> <!-- end card-->
    </div> <!-- end col -->
  </div>
</div>
