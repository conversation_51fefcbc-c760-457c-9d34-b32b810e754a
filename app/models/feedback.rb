# == Schema Information
#
# Table name: feedbacks
#
#  id          :bigint           not null, primary key
#  contact     :string(255)
#  description :text(65535)
#  images      :json
#  ip          :string(255)
#  pid         :integer
#  sn_code     :string(255)
#  status      :integer          default("pending"), not null
#  version     :string(255)
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#
class Feedback < ApplicationRecord
  # 状态枚举：待处理、处理中、已处理
  enum status: { pending: 0, processing: 1, completed: 2 }

  # 状态中文名称映射
  def self.status_options
    {
      'pending' => '待处理',
      'processing' => '处理中',
      'completed' => '已处理'
    }
  end

  # 获取状态的中文名称
  def status_i18n
    self.class.status_options[status] || status
  end

  def self.ransackable_attributes(auth_object = nil)
    ["contact", "description", "pid", "sn_code", "version", "ip", "status", "created_at", "updated_at"]
  end

  def self.ransackable_associations(auth_object = nil)
    []
  end
end
