class APIImageUploader < CarrierWave::Uploader::Base
  # Include MiniMagick support for image processing
  # include CarrierWave::MiniMagick

  # Override the directory where uploaded files will be stored
  def store_dir
    "uploads/api_images"
  end

  # Generate a secure filename
  def filename
    if original_filename.present?
      ext = File.extname(original_filename)
      "#{Time.now.strftime("%Y%m%d")}/#{secure_token}#{ext}"
    end
  end

  # Process files as they are uploaded
  # process resize_to_limit: [2048, 2048]

  # # Create different versions of uploaded images
  # version :thumb do
  #   process resize_to_fill: [300, 300]
  # end

  # version :medium do
  #   process resize_to_fill: [800, 600]
  # end

  # Add a whitelist of extensions which are allowed to be uploaded
  def extension_allowlist
    %w(jpg jpeg gif png webp)
  end

  # Limit file size to 30MB
  def size_range
    1.byte..30.megabytes
  end

  protected

  # Generate a random secure token
  def secure_token
    var = :"@#{mounted_as}_secure_token"
    model&.instance_variable_get(var) || model&.instance_variable_set(var, SecureRandom.base58(32)) || SecureRandom.base58(32)
  end
end
