# 图片上传 API 文档

## 概述

新增的图片上传 API 提供了单张和批量图片上传功能，使用新的 `ApiImageUploader` 上传器。

## 基础信息

- **基础路径**: `/api/uploads`
- **支持格式**: JPG, JPEG, PNG, GIF, WEBP
- **文件大小限制**: 10MB
- **图片处理**: 自动生成缩略图和中等尺寸版本

## API 端点

### 1. 单张图片上传

**POST** `/api/uploads/image`

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| image | File | 是 | 图片文件 |
| token | String | 否 | 用户Token（用于记录上传日志） |

#### 请求示例

```bash
curl -X POST \
  http://localhost:3000/api/uploads/image \
  -H 'Content-Type: multipart/form-data' \
  -F 'image=@/path/to/image.jpg' \
  -F 'token=user_authentication_token'
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "图片上传成功",
  "code": 200,
  "data": {
    "url": "https://example.com/uploads/api_images/20241214/abc123.jpg",
    "thumb_url": "https://example.com/uploads/api_images/20241214/thumb_abc123.jpg",
    "medium_url": "https://example.com/uploads/api_images/20241214/medium_abc123.jpg",
    "filename": "image.jpg",
    "size": 1024000,
    "uploaded_at": "2024-12-14T10:30:00Z",
    "user_id": 123
  }
}
```

**错误响应 (400)**:
```json
{
  "success": false,
  "message": "不支持的图片格式，请上传 JPG、PNG、GIF 或 WEBP 格式的图片",
  "code": 400
}
```

### 2. 批量图片上传

**POST** `/api/uploads/batch`

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| images | Array[File] | 是 | 图片文件数组（最多10张） |
| token | String | 否 | 用户Token（用于记录上传日志） |

#### 请求示例

```bash
curl -X POST \
  http://localhost:3000/api/uploads/batch \
  -H 'Content-Type: multipart/form-data' \
  -F 'images[]=@/path/to/image1.jpg' \
  -F 'images[]=@/path/to/image2.png' \
  -F 'token=user_authentication_token'
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "批量上传完成",
  "code": 200,
  "data": {
    "uploaded": [
      {
        "index": 0,
        "url": "https://example.com/uploads/api_images/20241214/abc123.jpg",
        "thumb_url": "https://example.com/uploads/api_images/20241214/thumb_abc123.jpg",
        "medium_url": "https://example.com/uploads/api_images/20241214/medium_abc123.jpg",
        "filename": "image1.jpg",
        "size": 1024000
      }
    ],
    "failed": [
      {
        "index": 1,
        "filename": "image2.txt",
        "message": "不支持的图片格式"
      }
    ],
    "total": 2,
    "success_count": 1,
    "failed_count": 1,
    "uploaded_at": "2024-12-14T10:30:00Z"
  }
}
```

## 图片版本说明

上传的图片会自动生成以下版本：

1. **原图**: 最大尺寸限制为 2048x2048
2. **缩略图 (thumb)**: 300x300，裁剪填充
3. **中等尺寸 (medium)**: 800x600，裁剪填充

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 400 | 请求参数错误（文件格式不支持、文件过大等） |
| 500 | 服务器内部错误 |

## 文件存储

- **存储路径**: `uploads/api_images/YYYYMMDD/`
- **文件命名**: 使用安全的随机token命名
- **云存储**: 生产环境使用阿里云OSS存储

## 使用注意事项

1. 支持的图片格式：JPG, JPEG, PNG, GIF, WEBP
2. 单个文件大小限制：10MB
3. 批量上传最多支持10张图片
4. 建议在上传前进行客户端文件格式和大小验证
5. 返回的URL可直接用于显示图片
6. 缩略图和中等尺寸版本适用于不同的显示场景
