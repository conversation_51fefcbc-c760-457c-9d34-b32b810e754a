# == Schema Information
#
# Table name: feedbacks
#
#  id          :bigint           not null, primary key
#  contact     :string(255)
#  description :text(65535)
#  images      :json
#  ip          :string(255)
#  pid         :integer
#  sn_code     :string(255)
#  status      :integer          default("pending"), not null
#  version     :string(255)
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#
FactoryBot.define do
  factory :feedback do
    description { "这是一个测试反馈" }
    contact { "<EMAIL>" }
    images { ["http://example.com/image1.jpg", "http://example.com/image2.jpg"] }
    ip { "***********" }
    version { "1.0.0" }
    sn_code { "SN123456789" }
    pid { 1 }
    status { :pending }

    trait :processing do
      status { :processing }
    end

    trait :completed do
      status { :completed }
    end
  end
end
