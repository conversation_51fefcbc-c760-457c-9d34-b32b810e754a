# == Schema Information
#
# Table name: feedbacks
#
#  id          :bigint           not null, primary key
#  contact     :string(255)
#  description :text(65535)
#  images      :json
#  ip          :string(255)
#  pid         :integer
#  sn_code     :string(255)
#  status      :integer          default("pending"), not null
#  version     :string(255)
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#
require "test_helper"

class FeedbackTest < ActiveSupport::TestCase
  def setup
    @feedback = create(:feedback)
  end

  test "should have default status as pending" do
    feedback = Feedback.new(description: "Test feedback")
    assert_equal "pending", feedback.status
  end

  test "should have valid status enum values" do
    assert_equal({ "pending" => 0, "processing" => 1, "completed" => 2 }, Feedback.statuses)
  end

  test "should return correct status_i18n" do
    @feedback.update(status: :pending)
    assert_equal "待处理", @feedback.status_i18n

    @feedback.update(status: :processing)
    assert_equal "处理中", @feedback.status_i18n

    @feedback.update(status: :completed)
    assert_equal "已处理", @feedback.status_i18n
  end

  test "should include status in ransackable_attributes" do
    assert_includes Feedback.ransackable_attributes, "status"
  end

  test "should have correct status_options" do
    expected_options = {
      'pending' => '待处理',
      'processing' => '处理中',
      'completed' => '已处理'
    }
    assert_equal expected_options, Feedback.status_options
  end
end
