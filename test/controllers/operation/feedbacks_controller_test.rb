require "test_helper"

class Operation::FeedbacksControllerTest < ActionDispatch::IntegrationTest
  def setup
    @admin_user = create(:admin_user)
    @feedback = create(:feedback)
    sign_in @admin_user
  end

  test "should get index" do
    get operation_feedbacks_path
    assert_response :success
    assert_includes response.body, @feedback.description
  end

  test "should get show" do
    get operation_feedback_path(@feedback)
    assert_response :success
    assert_includes response.body, @feedback.description
  end

  test "should filter by status" do
    pending_feedback = create(:feedback, status: :pending)
    processing_feedback = create(:feedback, :processing)
    completed_feedback = create(:feedback, :completed)

    # 测试待处理状态筛选
    get operation_feedbacks_path, params: { q: { status_eq: 'pending' } }
    assert_response :success
    assert_includes response.body, pending_feedback.description
    assert_not_includes response.body, processing_feedback.description

    # 测试处理中状态筛选
    get operation_feedbacks_path, params: { q: { status_eq: 'processing' } }
    assert_response :success
    assert_includes response.body, processing_feedback.description
    assert_not_includes response.body, pending_feedback.description

    # 测试已处理状态筛选
    get operation_feedbacks_path, params: { q: { status_eq: 'completed' } }
    assert_response :success
    assert_includes response.body, completed_feedback.description
    assert_not_includes response.body, pending_feedback.description
  end

  test "should update status via AJAX" do
    assert_equal "pending", @feedback.status

    patch update_status_operation_feedback_path(@feedback), 
          params: { status: "processing" },
          headers: { "Content-Type" => "application/json" },
          xhr: true

    assert_response :success
    @feedback.reload
    assert_equal "processing", @feedback.status

    response_data = JSON.parse(response.body)
    assert response_data["success"]
    assert_equal "状态更新成功", response_data["message"]
    assert_equal "processing", response_data["status"]
    assert_equal "处理中", response_data["status_i18n"]
  end

  test "should update status via regular request" do
    assert_equal "pending", @feedback.status

    patch update_status_operation_feedback_path(@feedback), 
          params: { status: "completed" }

    assert_redirected_to operation_feedback_path(@feedback)
    @feedback.reload
    assert_equal "completed", @feedback.status
  end

  test "should handle invalid status update" do
    patch update_status_operation_feedback_path(@feedback), 
          params: { status: "invalid_status" },
          headers: { "Content-Type" => "application/json" },
          xhr: true

    assert_response :success
    response_data = JSON.parse(response.body)
    assert_not response_data["success"]
  end
end
