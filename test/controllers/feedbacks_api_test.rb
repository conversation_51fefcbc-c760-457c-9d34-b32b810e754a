require "test_helper"

class FeedbacksApiTest < ActionDispatch::IntegrationTest
  def setup
    @feedback = create(:feedback)
  end

  test "should create feedback via API" do
    feedback_params = {
      description: "测试问题描述",
      contact: "<EMAIL>",
      pid: 123,
      sn_code: "SN123456",
      version: "1.0.0",
      images: ["http://example.com/image1.jpg"]
    }

    assert_difference('Feedback.count') do
      post "/api/feedbacks", params: feedback_params
    end

    assert_response :created
    response_data = JSON.parse(response.body)
    assert_equal 201, response_data['status']
    assert_equal '反馈提交成功', response_data['message']
    assert response_data['data']['id'].present?

    # 验证新创建的反馈默认状态为pending
    created_feedback = Feedback.find(response_data['data']['id'])
    assert_equal "pending", created_feedback.status
  end

  test "should get feedbacks list via API" do
    get "/api/feedbacks"

    assert_response :success
    response_data = JSON.parse(response.body)
    assert_equal 200, response_data['status']
    assert response_data['data'].is_a?(Array)
    assert response_data['pagination'].present?
  end

  test "should get feedback detail via API" do
    get "/api/feedbacks/#{@feedback.id}"

    assert_response :success
    response_data = JSON.parse(response.body)
    assert_equal 200, response_data['status']
    assert_equal @feedback.id, response_data['data']['id']
    assert_equal @feedback.description, response_data['data']['description']
  end

  test "should return 404 for non-existent feedback" do
    get "/api/feedbacks/99999"

    assert_response :not_found
  end

  test "should filter feedbacks by pid" do
    create(:feedback, pid: 456)

    get "/api/feedbacks", params: { pid: 456 }

    assert_response :success
    response_data = JSON.parse(response.body)
    assert_equal 200, response_data['status']
    response_data['data'].each do |feedback|
      assert_equal 456, feedback['pid']
    end
  end

  test "should filter feedbacks by sn_code" do
    create(:feedback, sn_code: "SPECIAL123")

    get "/api/feedbacks", params: { sn_code: "SPECIAL123" }

    assert_response :success
    response_data = JSON.parse(response.body)
    assert_equal 200, response_data['status']
    response_data['data'].each do |feedback|
      assert_equal "SPECIAL123", feedback['sn_code']
    end
  end

  test "should handle pagination" do
    # 创建多个反馈记录
    15.times { create(:feedback) }

    get "/api/feedbacks", params: { page: 1, per: 5 }

    assert_response :success
    response_data = JSON.parse(response.body)
    assert_equal 200, response_data['status']
    assert_equal 5, response_data['data'].length
    assert_equal 1, response_data['pagination']['current_page']
    assert_equal 5, response_data['pagination']['per_page']
    assert response_data['pagination']['total_count'] >= 15
  end

  test "should require description for feedback creation" do
    feedback_params = {
      contact: "<EMAIL>",
      pid: 123
      # 缺少 description
    }

    assert_no_difference('Feedback.count') do
      post "/api/feedbacks", params: feedback_params
    end

    assert_response :bad_request
  end
end
