Rails.application.routes.draw do
  namespace :operation do
    resources :devices
    resources :firmwares do
      member do
        get :audits
      end
    end
  end
  resources :customize
  # devise_for :admin_users
  require 'sidekiq/web'
  mount Sidekiq::Web => '/sidekiq'
  Sidekiq::Web.use(Rack::Auth::Basic) do |user, password|
    [user, password] == ["sidekiqadmin", "yourpassword"]
  end

  devise_for :admin_users, controllers: { sessions: 'admin_users/sessions' }
  mount API => "/"
  mount GrapeSwaggerRails::Engine => "/Docapi"

  post '/api/emqx_events', to: 'emqx#events'
  resources :orders do
    post :create_update
    post :wechat_notify, on: :collection
    post :wechat_refund_notify, on: :collection
  end

  namespace :operation, path: "/operation" do
    root "main#dashboard"
    resources :main, only: [] do
      collection do
        get :dashboard
        get :subscription_statistic
        get :sale_count_statistic
      end
    end
    resources :admin_users
    resources :career_presets, except: %i[show]
    resources :logs, only: [] do
      collection do
        get :login_logs
        post :forbidden_address
      end
    end
    resources :app_versions
    resources :opinions, only: :index do
      post :update_right_reply, on: :member
      post :mark, on: :member
    end

    resources :users do
      post :users_status
    end

    resources :products do
      post :show_status
      post :product_status
      member do
        patch :update_que
      end

      resources :comments do
        post :comments_status
        post :comments_is_owner
      end
    end

    resources :tags
    resources :banners do
      member do
        patch :update_que
        patch :show_status
      end
    end
    resources :crowds
    resources :crowd_statuses do
      collection do
        patch :update_que
      end
    end
    resources :orders do
      post :refunds
      member do
        post :cancel_refund
      end
    end
    resources :manage_roles do
      patch :update_auths, on: :member
    end

    resources :system_configs, only: %i[index update]
    resources :push_messages do
      get :template_content, on: :collection
      put :revoke, on: :member
    end
    resources :tokens
    resources :feedbacks, only: [:index, :show, :destroy] do
      member do
        patch :update_status
      end
    end
  end
  post :guanjia, to: "guanjia#index"
  post :uploads, to: "operation/uploads#create"

  namespace :web do
    get :activity_url, to: 'activity_url#index'
    resources :products, only: %i[show]
  end

  get "/403", to: "operation/exceptions#forbidden"
  get ':id', to: "web/short_urls#show"
end
