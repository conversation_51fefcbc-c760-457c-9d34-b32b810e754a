require 'rails_helper'

describe Uploads do
  let(:user) { create(:user) }
  let(:test_image_path) { Rails.root.join('public', 'apple-touch-icon.png') }
  
  describe 'POST /api/uploads/image' do
    context 'with valid image file' do
      it 'uploads image successfully' do
        image_file = Rack::Test::UploadedFile.new(test_image_path, 'image/png')
        
        post '/api/uploads/image', params: { image: image_file }
        
        expect(response.status).to eq(201)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be true
        expect(json_response['code']).to eq(200)
        expect(json_response['data']['url']).to be_present
        expect(json_response['data']['thumb_url']).to be_present
        expect(json_response['data']['medium_url']).to be_present
        expect(json_response['data']['filename']).to eq('apple-touch-icon.png')
      end
      
      it 'uploads image with user token' do
        image_file = Rack::Test::UploadedFile.new(test_image_path, 'image/png')
        
        post '/api/uploads/image', params: { 
          image: image_file, 
          token: user.authentication_token 
        }
        
        expect(response.status).to eq(201)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be true
        expect(json_response['data']['user_id']).to eq(user.id)
      end
    end
    
    context 'with invalid file' do
      it 'rejects non-image files' do
        text_file = Rack::Test::UploadedFile.new(
          StringIO.new('test content'), 
          'text/plain', 
          original_filename: 'test.txt'
        )
        
        post '/api/uploads/image', params: { image: text_file }
        
        expect(response.status).to eq(201)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be false
        expect(json_response['code']).to eq(400)
        expect(json_response['message']).to include('不支持的图片格式')
      end
      
      it 'rejects missing file' do
        post '/api/uploads/image', params: {}
        
        expect(response.status).to eq(201)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be false
        expect(json_response['code']).to eq(400)
        expect(json_response['message']).to include('请选择要上传的图片')
      end
    end
  end
  
  describe 'POST /api/uploads/batch' do
    context 'with valid image files' do
      it 'uploads multiple images successfully' do
        image_file1 = Rack::Test::UploadedFile.new(test_image_path, 'image/png')
        image_file2 = Rack::Test::UploadedFile.new(test_image_path, 'image/png')
        
        post '/api/uploads/batch', params: { 
          images: [image_file1, image_file2] 
        }
        
        expect(response.status).to eq(201)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be true
        expect(json_response['data']['success_count']).to eq(2)
        expect(json_response['data']['failed_count']).to eq(0)
        expect(json_response['data']['uploaded'].length).to eq(2)
      end
      
      it 'handles mixed valid and invalid files' do
        image_file = Rack::Test::UploadedFile.new(test_image_path, 'image/png')
        text_file = Rack::Test::UploadedFile.new(
          StringIO.new('test content'), 
          'text/plain', 
          original_filename: 'test.txt'
        )
        
        post '/api/uploads/batch', params: { 
          images: [image_file, text_file] 
        }
        
        expect(response.status).to eq(201)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be true
        expect(json_response['data']['success_count']).to eq(1)
        expect(json_response['data']['failed_count']).to eq(1)
      end
    end
    
    context 'with too many files' do
      it 'rejects more than 10 files' do
        images = Array.new(11) do
          Rack::Test::UploadedFile.new(test_image_path, 'image/png')
        end
        
        post '/api/uploads/batch', params: { images: images }
        
        expect(response.status).to eq(201)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be false
        expect(json_response['code']).to eq(400)
        expect(json_response['message']).to include('一次最多只能上传10张图片')
      end
    end
  end
end
