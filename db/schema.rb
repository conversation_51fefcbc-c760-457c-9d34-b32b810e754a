# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_08_27_000001) do
  create_table "admin_users", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "email", collation: "utf8mb3_general_ci"
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "is_admin"
    t.string "account"
    t.string "name"
    t.index ["account"], name: "index_admin_users_on_account", unique: true
    t.index ["email"], name: "index_admin_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_admin_users_on_reset_password_token", unique: true
  end

  create_table "after_sales", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "order_id"
    t.integer "order_status"
    t.string "reason"
    t.string "detail"
    t.float "amout"
    t.integer "status"
    t.datetime "return_at", precision: nil
    t.datetime "not_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "asset_imgs_count", default: 0
  end

  create_table "app_versions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "ver_type", default: 0
    t.string "version"
    t.string "node"
    t.string "file_load"
    t.integer "download_count"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "title"
  end

  create_table "assembles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "product_id"
    t.integer "user_id"
    t.boolean "is_like", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "asset_imgs", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "image"
    t.integer "resoure_id"
    t.string "resoure_type"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "audits", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "auditable_id"
    t.string "auditable_type"
    t.integer "associated_id"
    t.string "associated_type"
    t.integer "user_id"
    t.string "user_type"
    t.string "username"
    t.string "action"
    t.text "audited_changes"
    t.integer "version", default: 0
    t.string "comment"
    t.string "remote_address"
    t.string "request_uuid"
    t.datetime "created_at"
    t.index ["associated_type", "associated_id"], name: "associated_index"
    t.index ["auditable_type", "auditable_id", "version"], name: "auditable_index"
    t.index ["created_at"], name: "index_audits_on_created_at"
    t.index ["request_uuid"], name: "index_audits_on_request_uuid"
    t.index ["user_id", "user_type"], name: "user_index"
  end

  create_table "banners", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "image"
    t.string "picture_url"
    t.string "title"
    t.integer "product_id"
    t.integer "click_count", default: 0
    t.integer "que", default: 0
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "crowd_id"
    t.boolean "is_closing", default: false
    t.integer "banner_type"
    t.text "content"
  end

  create_table "career_presets", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "background"
    t.string "title_zh"
    t.text "description_zh"
    t.string "title_en"
    t.text "description_en"
    t.string "title_tw"
    t.text "description_tw"
    t.string "title_ja"
    t.text "description_ja"
    t.string "title_ko"
    t.text "description_ko"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "deploy_env"
    t.text "description_pt"
    t.string "title_pt"
  end

  create_table "comments", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "product_id"
    t.integer "sku_id"
    t.integer "user_id"
    t.string "user_nickname"
    t.boolean "is_buy", default: true
    t.string "content"
    t.integer "userlikes_count", default: 0
    t.integer "parent_id"
    t.integer "comments_count", default: 0
    t.integer "que"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "asset_imgs_count", default: 0
    t.boolean "status", default: true
    t.boolean "is_owner", default: false
  end

  create_table "connection_logs", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "device_id"
    t.string "status"
    t.datetime "timestamp"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "crowd_statuses", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "icon"
    t.integer "que"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "crowds", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "product_id"
    t.integer "sku_id"
    t.string "name"
    t.string "image"
    t.boolean "is_time_come", default: false
    t.datetime "start_time", precision: nil
    t.datetime "end_time", precision: nil
    t.integer "end_status", default: 0
    t.integer "status", default: 0
    t.integer "que", default: 0
    t.integer "count", default: 0
    t.integer "subscriptions_count", default: 0
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "color_value", default: "#fff"
    t.integer "sale_count", default: 0
    t.integer "crowd_status_id"
    t.index ["crowd_status_id"], name: "index_crowds_on_crowd_status_id"
  end

  create_table "devices", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "pid"
    t.string "dfu_pid"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "feedbacks", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "description"
    t.string "contact"
    t.json "images"
    t.string "ip"
    t.string "version"
    t.string "sn_code"
    t.integer "pid"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "status", default: 0, null: false
    t.index ["status"], name: "index_feedbacks_on_status"
  end

  create_table "firmwares", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "version"
    t.boolean "force_update"
    t.string "file"
    t.string "title_zh"
    t.text "description_zh"
    t.string "title_en"
    t.text "description_en"
    t.string "title_tw"
    t.text "description_tw"
    t.string "title_ja"
    t.text "description_ja"
    t.string "title_ko"
    t.text "description_ko"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "deploy_env"
    t.integer "device_id"
    t.text "description_pt"
  end

  create_table "keycap_configs", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "keycap_id"
    t.integer "career_preset_id"
    t.boolean "enable_rt_mode"
    t.float "trigger_point"
    t.float "press_trigger_point"
    t.float "release_trigger_point"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["career_preset_id"], name: "index_keycap_configs_on_career_preset_id"
    t.index ["keycap_id"], name: "index_keycap_configs_on_keycap_id"
  end

  create_table "keycaps", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "login_logs", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "ip"
    t.datetime "login_at"
    t.string "address"
    t.boolean "is_forbidden"
    t.boolean "is_lastest"
    t.integer "admin_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_user_id"], name: "index_login_logs_on_admin_user_id"
  end

  create_table "manage_actions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "word"
    t.integer "manage_controller_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "que"
    t.index ["manage_controller_id"], name: "index_manage_actions_on_manage_controller_id"
  end

  create_table "manage_controllers", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "word"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "que"
  end

  create_table "manage_role_actions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "manage_role_id"
    t.integer "manage_action_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["manage_action_id"], name: "index_manage_role_actions_on_manage_action_id"
    t.index ["manage_role_id"], name: "index_manage_role_actions_on_manage_role_id"
  end

  create_table "manage_role_users", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "manage_role_id"
    t.integer "admin_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_user_id"], name: "index_manage_role_users_on_admin_user_id"
    t.index ["manage_role_id"], name: "index_manage_role_users_on_manage_role_id"
  end

  create_table "manage_roles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "notifications", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "user_id"
    t.integer "notify_type"
    t.text "content"
    t.boolean "is_read", default: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "send_user_id"
    t.integer "target_id"
    t.string "target_type"
    t.index ["send_user_id"], name: "index_notifications_on_send_user_id"
    t.index ["target_id", "target_type"], name: "index_notifications_on_target_id_and_target_type"
  end

  create_table "opinions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "user_id"
    t.integer "option_type"
    t.text "detail"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "mobile"
    t.integer "asset_imgs_count", default: 0
    t.integer "status", default: 0
    t.integer "parent_id"
    t.string "code"
    t.boolean "is_mark"
    t.index ["parent_id"], name: "index_opinions_on_parent_id"
  end

  create_table "order_details", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "order_id"
    t.integer "product_id"
    t.integer "sku_id"
    t.string "product_name"
    t.string "property_name"
    t.string "sku_number"
    t.string "skupictureurl"
    t.float "skuprice"
    t.integer "sku_count"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "orders", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "number"
    t.integer "count"
    t.float "amount"
    t.integer "user_id"
    t.string "user_nickname"
    t.string "phone"
    t.datetime "pay_time", precision: nil
    t.datetime "delivery_time", precision: nil
    t.datetime "send_time", precision: nil
    t.datetime "refund_time", precision: nil
    t.datetime "charge_time", precision: nil
    t.string "charge_number"
    t.string "pay_type"
    t.integer "receive_id"
    t.string "receive_name"
    t.string "receive_phone"
    t.string "receive_address"
    t.string "logistics_com"
    t.string "logistics_number"
    t.string "logistics_desp"
    t.string "user_node"
    t.string "admin_node"
    t.integer "status"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "refund_id"
    t.integer "crowd_id"
    t.text "text_conment"
    t.integer "order_type"
  end

  create_table "payments", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "trade_no"
    t.float "amount"
    t.integer "order_id"
    t.integer "pay_method"
    t.datetime "pay_at"
    t.datetime "refund_at"
    t.integer "status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["order_id"], name: "index_payments_on_order_id"
  end

  create_table "product_images", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "picture"
    t.integer "product_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "products", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.string "title"
    t.string "button_txt"
    t.string "before_image"
    t.string "share_img_url"
    t.string "list_img_url"
    t.text "detail"
    t.integer "sale_count", default: 0
    t.boolean "is_sale", default: false
    t.boolean "on_shelf", default: false
    t.boolean "on_sale", default: false
    t.boolean "is_comment", default: false
    t.boolean "is_open_comment", default: false
    t.integer "tag_id"
    t.integer "que", default: 0
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "assembles_count"
    t.integer "total_inventory", default: 0
    t.integer "subscriptions_count", default: 0
    t.string "aftermarket"
    t.boolean "is_pre_sale"
    t.integer "pre_sale_stage"
    t.float "deposit"
    t.datetime "is_sale_at"
    t.boolean "is_sale_at_button"
    t.string "qrcode"
    t.string "url_link"
  end

  create_table "profiles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "share_code"
    t.integer "device_id"
    t.json "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["device_id"], name: "index_profiles_on_device_id"
  end

  create_table "properties", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.integer "level", default: 0
    t.integer "que", default: 0
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "parent_id"
  end

  create_table "push_messages", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "title"
    t.boolean "include_sms_message"
    t.boolean "include_jpush"
    t.integer "template_id"
    t.integer "product_id"
    t.text "template_content"
    t.string "msg_id"
    t.datetime "pushed_at"
    t.json "crowd"
    t.integer "expected_push_count"
    t.integer "actual_push_count"
    t.integer "timing"
    t.integer "status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["status"], name: "index_push_messages_on_status"
  end

  create_table "receives", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "region_id"
    t.string "name"
    t.string "phone"
    t.string "address"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "user_id"
    t.boolean "is_default", default: false
  end

  create_table "regions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "level"
    t.string "name"
    t.integer "parent_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "reports", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "resoure_id"
    t.string "resoure_type"
    t.integer "report_code"
    t.integer "user_id"
    t.string "report_desc"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "short_url_statistics", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "ip"
    t.integer "short_url_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["short_url_id"], name: "index_short_url_statistics_on_short_url_id"
  end

  create_table "short_urls", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "uniq_key"
    t.string "redirect_to"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "click_count", default: 0
    t.index ["uniq_key"], name: "index_short_urls_on_uniq_key", unique: true
  end

  create_table "sku_properties", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "property_id"
    t.integer "sku_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "skus", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "sku_outerid"
    t.string "sku_name"
    t.string "sku_number"
    t.string "sku_property"
    t.string "sku_pictureurl"
    t.float "sku_markedprice"
    t.float "sku_price"
    t.integer "sku_quantity", default: 0
    t.integer "product_id"
    t.integer "que", default: 0
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "properties_ids"
  end

  create_table "subscriptions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "product_id"
    t.integer "sku_id"
    t.integer "user_id"
    t.boolean "is_subscription", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "system_configs", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.boolean "auto_refund"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "tags", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.integer "que", default: 0
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "tokens", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "password", null: false
    t.datetime "expired_at"
    t.string "user_id", null: false
    t.string "share_url"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "device_id"
    t.index ["device_id"], name: "index_tokens_on_device_id"
    t.index ["expired_at"], name: "index_tokens_on_expired_at"
    t.index ["password"], name: "index_tokens_on_password"
    t.index ["user_id"], name: "index_tokens_on_user_id"
  end

  create_table "user_operations", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "user_id"
    t.date "created_on"
    t.integer "operation_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_user_operations_on_user_id"
  end

  create_table "user_statistics", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.date "created_on"
    t.integer "total_count"
    t.integer "stat_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "userlikes", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "resoure_id"
    t.string "resoure_type"
    t.integer "user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "users", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "user_nikename", collation: "utf8mb4_unicode_ci"
    t.string "avatar"
    t.string "phone"
    t.string "openid"
    t.string "wx_name", collation: "utf8mb4_unicode_ci"
    t.boolean "status", default: false
    t.integer "option_count", default: 0
    t.integer "user_login_time", default: 0
    t.string "user_system", default: "", null: false
    t.string "user_version", default: "", null: false
    t.integer "comments_count", default: 0
    t.integer "orders_count", default: 0
    t.integer "subscriptions_count", default: 0
    t.integer "assembles_count", default: 0
    t.float "amounts_count", default: 0.0
    t.datetime "login_time", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "authentication_token"
    t.string "register_id"
    t.string "avatar_file"
    t.string "newup_name"
    t.string "username"
    t.string "unionid"
    t.string "mini_program_openid"
    t.integer "source_from"
    t.index ["openid"], name: "index_users_on_openid", unique: true
    t.index ["phone"], name: "index_users_on_phone", unique: true
    t.index ["username"], name: "index_users_on_username", unique: true
  end

  create_table "verification_codes", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "code"
    t.string "phone"
    t.string "resource_type"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

end
